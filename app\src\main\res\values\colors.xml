<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 基础颜色 - 这些是标准的颜色值 -->
    <color name="color_black">#FF000000</color>
    <color name="color_white">#FFFFFFFF</color>
    <color name="color_transparent">#00000000</color>
    <color name="color_gray_50">#FAFAFA</color>
    <color name="color_gray_100">#F5F5F5</color>
    <color name="color_gray_200">#EEEEEE</color>
    <color name="color_gray_300">#E0E0E0</color>
    <color name="color_gray_400">#BDBDBD</color>
    <color name="color_gray_500">#9E9E9E</color>
    <color name="color_gray_600">#757575</color>
    <color name="color_gray_700">#616161</color>
    <color name="color_gray_800">#424242</color>
    <color name="color_gray_900">#212121</color>
    <color name="color_blue_500">#2196F3</color>
    <color name="color_blue_700">#1976D2</color>
    <color name="color_blue_900">#0D47A1</color>
    <color name="color_light_blue_500">#03A9F4</color>
    <color name="color_light_blue_700">#0288D1</color>
    <color name="color_pink_500">#E91E63</color>
    <color name="color_pink_700">#C2185B</color>
    <color name="color_red_500">#F44336</color>
    <color name="color_green_500">#4CAF50</color>
    <color name="color_yellow_500">#FFEB3B</color>
    <color name="color_purple_200">#FFBB86FC</color>
    <color name="color_purple_500">#FF6200EE</color>
    <color name="color_purple_700">#FF3700B3</color>
    <color name="color_teal_200">#FF03DAC5</color>
    <color name="color_teal_700">#FF018786</color>

    <!-- 主题颜色 - 应用的主要颜色方案 -->
    <color name="theme_primary">#1E88E5</color>
    <color name="theme_primary_dark">#0D47A1</color>
    <color name="theme_primary_light">#BBDEFB</color>
    <color name="theme_accent">#42A5F5</color>
    <color name="theme_accent_dark">#1976D2</color>
    <color name="theme_accent_light">#90CAF9</color>

    <!-- 收藏红色 -->
    <color name="favorite_red">#F44336</color>
    <color name="favorite_red_light">#FFCDD2</color>
    <color name="theme_background">#F5F5F5</color>
    <color name="theme_surface">#FFFFFF</color>
    <color name="theme_error">#F44336</color>
    <color name="theme_success">#4CAF50</color>
    <color name="theme_warning">#FFC107</color>
    <color name="theme_info">#2196F3</color>

    <!-- 文本颜色 -->
    <color name="text_primary">#212121</color>
    <color name="text_secondary">#757575</color>
    <color name="text_hint">#BDBDBD</color>
    <color name="text_disabled">#9E9E9E</color>
    <color name="text_light">#FFFFFF</color>
    <color name="text_dark">#000000</color>
    <color name="text_link">#2196F3</color>

    <!-- UI元素颜色 -->
    <color name="ui_divider">#E0E0E0</color>
    <color name="ui_ripple">#80FFFFFF</color>
    <color name="ui_button">#2196F3</color>
    <color name="ui_button_text">#FFFFFF</color>
    <color name="ui_splash_background">#1E3A5F</color>

    <!-- 搜索框颜色 -->
    <color name="search_background">#F8F9FA</color>
    <color name="search_stroke">#DADCE0</color>

    <!-- 导航颜色 -->
    <color name="nav_background">#000000</color>
    <color name="nav_icon_active">#1E88E5</color>
    <color name="nav_icon_inactive">#787878</color>
    <color name="nav_indicator">#1E88E5</color>

    <!-- 侧边栏颜色 -->
    <color name="sidebar_background">#000000</color>
    <color name="sidebar_item_text">#FFFFFF</color>
    <color name="sidebar_item_icon_normal">#AAAAAA</color>
    <color name="sidebar_item_icon_selected">#2196F3</color>
    <color name="sidebar_item_background_selected">#303030</color>

    <!-- 播放器颜色 -->
    <color name="player_background">#121212</color>
    <color name="player_controls">#FFFFFF</color>
    <color name="player_progress_background">#4D4D4D</color>
    <color name="player_progress">#2196F3</color>
    <color name="player_control_background">#EDEDED</color>

    <!-- 歌词颜色 -->
    <color name="lyric_background">#212121</color>
    <color name="lyric_highlight">#2196F3</color>
    <color name="lyric_normal">#9E9E9E</color>

    <!-- 列表项颜色 -->
    <color name="list_item_background">#FFFFFF</color>
    <color name="list_item_background_selected">#E3F2FD</color>

    <!-- 驾驶模式颜色 -->
    <color name="driving_mode_background">#000000</color>
    <color name="driving_mode_text">#FFFFFF</color>
    <color name="driving_mode_controls">#2196F3</color>

    <!-- 樱花主题颜色 -->
    <color name="sakura_primary">#FFBAD3</color>
    <color name="sakura_primary_dark">#FF9CB6</color>
    <color name="sakura_accent">#FE8BC6</color>
    <color name="sakura_background">#FFF5F8</color>
    <color name="sakura_text_primary">#66304D</color>
    <color name="sakura_text_secondary">#976681</color>
    <color name="sakura_divider">#FFE4EE</color>

    <!-- 兼容性映射 - 保持向后兼容性 -->
    <!-- 标准Android颜色 -->
    <color name="purple_200">@color/color_purple_200</color>
    <color name="purple_500">@color/color_purple_500</color>
    <color name="purple_700">@color/color_purple_700</color>
    <color name="teal_200">@color/color_teal_200</color>
    <color name="teal_700">@color/color_teal_700</color>
    <color name="black">@color/color_black</color>
    <color name="white">@color/color_white</color>
    <color name="transparent">@color/color_transparent</color>
    <color name="gray">@color/color_gray_500</color>
    <color name="light_gray">@color/color_gray_300</color>
    <color name="dark_gray">@color/color_gray_700</color>
    <color name="red">@color/color_red_500</color>
    <color name="green">@color/color_green_500</color>
    <color name="blue">@color/color_blue_500</color>
    <color name="yellow">@color/color_yellow_500</color>

    <!-- 旧版主题颜色 -->
    <color name="primary">@color/theme_primary</color>
    <color name="primary_dark">@color/theme_primary_dark</color>
    <color name="primary_light">@color/theme_primary_light</color>
    <color name="accent">@color/theme_accent</color>
    <color name="accent_dark">@color/theme_accent_dark</color>
    <color name="accent_light">@color/theme_accent_light</color>
    <color name="background">@color/theme_background</color>
    <color name="divider">@color/ui_divider</color>
    <color name="success">@color/theme_success</color>
    <color name="info">@color/theme_info</color>
    <color name="warning">@color/theme_warning</color>
    <color name="error">@color/theme_error</color>

    <!-- 新增UI主题颜色兼容 -->
    <color name="primary_color">@color/theme_primary</color>
    <color name="primary_dark_color">@color/theme_primary_dark</color>
    <color name="accent_color">@color/theme_accent</color>
    <color name="background_color">@color/theme_background</color>
    <color name="text_primary_color">@color/text_primary</color>
    <color name="text_secondary_color">@color/text_secondary</color>
    <color name="divider_color">@color/ui_divider</color>
    <color name="button_color">@color/ui_button</color>
    <color name="button_text_color">@color/ui_button_text</color>
    <color name="ripple_color">@color/ui_ripple</color>
    <color name="splash_background">@color/ui_splash_background</color>
    <color name="lyric_background_color">@color/lyric_background</color>
    <color name="lyric_highlight_color">@color/lyric_highlight</color>
    <color name="lyric_normal_color">@color/lyric_normal</color>

    <!-- 搜索框颜色兼容 -->
    <color name="search_bg_color">@color/search_background</color>
    <color name="search_stroke_color">@color/search_stroke</color>

    <!-- 导航栏颜色兼容 -->
    <color name="colorPrimary">@color/theme_primary</color>
    <color name="colorPrimaryDark">@color/theme_primary_dark</color>
    <color name="colorAccent">@color/theme_accent</color>

    <!-- 解决资源链接错误添加的颜色 -->
    <color name="colorBackground">@color/theme_background</color>
    <color name="colorTextPrimary">@color/text_primary</color>
    <color name="colorTextSecondary">@color/text_secondary</color>

    <!-- 百度语音SDK需要的颜色 -->
    <color name="color_background">@color/theme_background</color>

    <!-- 评论和心动模式相关颜色 -->
    <color name="background_dark">#121212</color>
    <color name="background_dark_lighter">#1E1E1E</color>
    <color name="toolbar_background">#1A1A1A</color>
    <color name="card_background">#2A2A2A</color>
</resources>