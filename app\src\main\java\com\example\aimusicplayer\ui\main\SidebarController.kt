package com.example.aimusicplayer.ui.main

import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.view.ViewGroup
import android.widget.RelativeLayout
import androidx.lifecycle.MutableLiveData
import com.example.aimusicplayer.R

/**
 * 侧边栏控制器
 * 负责侧边栏的显示/隐藏逻辑
 * 从Activity迁移到ViewModel的业务逻辑
 */
class SidebarController {
    companion object {
        private const val TAG = "SidebarController"

        // 侧边栏自动隐藏延迟时间（毫秒）
        private const val NAVBAR_AUTO_HIDE_DELAY = 5000L
    }

    // 侧边栏状态的LiveData
    private val isVisible = MutableLiveData(false)

    // 侧边栏UI组件
    private var sidebarNav: LinearLayout? = null
    private var btnMenuRight: ImageView? = null
    private var fragmentContainer: FrameLayout? = null
    private var dimOverlay: View? = null

    // 导航按钮
    private var navButtons: Array<View>? = null

    // 自动隐藏侧边栏的Handler
    private val navbarHideHandler = Handler(Looper.getMainLooper())

    // 防止快速连续点击
    private var isToggling = false

    /**
     * 初始化UI组件
     * @param sidebarNav 侧边栏视图
     * @param btnMenuRight 菜单按钮
     * @param fragmentContainer Fragment容器
     * @param navButtons 导航按钮数组
     */
    fun initializeViews(
        sidebarNav: LinearLayout,
        btnMenuRight: ImageView,
        fragmentContainer: FrameLayout,
        navButtons: Array<View>
    ) {
        this.sidebarNav = sidebarNav
        this.btnMenuRight = btnMenuRight
        this.fragmentContainer = fragmentContainer
        this.navButtons = navButtons

        // 创建暗淡覆盖层
        createDimOverlay()

        // 初始状态下隐藏侧边栏
        sidebarNav.post {
            sidebarNav.translationX = -sidebarNav.width.toFloat()
            sidebarNav.visibility = View.VISIBLE
            sidebarNav.isClickable = false
            sidebarNav.isEnabled = false

            // 禁用所有导航按钮点击事件
            navButtons.forEach { button ->
                button.isClickable = false
            }
        }

        // 设置菜单按钮初始状态
        btnMenuRight.apply {
            // 使用三条横线的菜单图标
            setImageResource(R.drawable.ic_menu)
            val buttonParams = layoutParams as RelativeLayout.LayoutParams
            buttonParams.removeRule(RelativeLayout.RIGHT_OF)
            buttonParams.addRule(RelativeLayout.ALIGN_PARENT_LEFT)
            buttonParams.leftMargin = 16
            layoutParams = buttonParams
        }
    }

    /**
     * 切换侧边栏状态 (简化版)
     */
    fun toggleSidebar() {
        // 如果正在切换中，忽略此次点击
        if (isToggling) {
            return
        }

        val currentState = isVisible.value ?: false
        if (currentState) {
            hideSidebar()
        } else {
            showSidebar()
        }
    }

    /**
     * 显示侧边栏 (简化版)
     */
    fun showSidebar() {
        val sidebar = sidebarNav ?: run {
            Log.e(TAG, "无法显示侧边栏：sidebarNav为null")
            return
        }

        val currentState = isVisible.value ?: false
        if (currentState) {
            // 已经是显示状态，不需要再次显示
            return
        }

        Log.d(TAG, "开始显示侧边栏")
        isToggling = true

        // 确保侧边栏可见
        sidebar.visibility = View.VISIBLE

        // 显示暗淡覆盖层
        showDimOverlay()

        // 优化的显示动画，使用硬件加速和更流畅的插值器
        sidebar.animate()
            .translationX(0f)
            .setDuration(300) // 增加动画时长，更流畅
            .setInterpolator(android.view.animation.DecelerateInterpolator(2f)) // 更强的减速效果
            .withStartAction {
                // 启用硬件加速以提高动画性能
                sidebar.setLayerType(View.LAYER_TYPE_HARDWARE, null)
                // 添加轻微的缩放效果
                sidebar.scaleX = 0.95f
                sidebar.scaleY = 0.95f
                sidebar.alpha = 0.8f
            }
            .withEndAction {
                isVisible.value = true
                isToggling = false

                // 隐藏菜单按钮
                btnMenuRight?.visibility = View.GONE

                // 启用侧边栏和导航按钮
                sidebar.isClickable = true
                sidebar.isEnabled = true
                navButtons?.forEach { button ->
                    button.isClickable = true
                }

                // 动画结束后恢复默认渲染模式
                sidebar.setLayerType(View.LAYER_TYPE_NONE, null)

                // 开始自动隐藏计时器
                scheduleAutoHide()

                Log.d(TAG, "侧边栏显示完成")
            }
            .start()

        // 同时执行缩放和透明度动画
        sidebar.animate()
            .scaleX(1f)
            .scaleY(1f)
            .alpha(1f)
            .setDuration(300)
            .setInterpolator(android.view.animation.OvershootInterpolator(0.5f))
            .start()
    }

    /**
     * 隐藏侧边栏 (简化版)
     */
    fun hideSidebar() {
        val sidebar = sidebarNav ?: run {
            Log.e(TAG, "无法隐藏侧边栏：sidebarNav为null")
            return
        }

        val currentState = isVisible.value ?: false
        if (!currentState) {
            // 已经是隐藏状态，不需要再次隐藏
            return
        }

        Log.d(TAG, "开始隐藏侧边栏")
        isToggling = true

        // 取消自动隐藏计时器
        navbarHideHandler.removeCallbacksAndMessages(null)

        // 隐藏暗淡覆盖层
        hideDimOverlay()

        // 立即禁用点击事件
        sidebar.isClickable = false
        sidebar.isEnabled = false
        navButtons?.forEach { button ->
            button.isClickable = false
        }

        // 优化的隐藏动画，使用硬件加速和更流畅的效果
        sidebar.animate()
            .translationX(-sidebar.width.toFloat())
            .scaleX(0.9f)
            .scaleY(0.9f)
            .alpha(0.5f)
            .setDuration(300) // 增加动画时长，更流畅
            .setInterpolator(android.view.animation.AccelerateInterpolator(2f)) // 更强的加速效果
            .withStartAction {
                // 启用硬件加速以提高动画性能
                sidebar.setLayerType(View.LAYER_TYPE_HARDWARE, null)
            }
            .withEndAction {
                isVisible.value = false
                isToggling = false

                // 重置侧边栏状态
                sidebar.scaleX = 1f
                sidebar.scaleY = 1f
                sidebar.alpha = 1f

                // 动画结束后恢复默认渲染模式
                sidebar.setLayerType(View.LAYER_TYPE_NONE, null)

                // 重新显示菜单按钮，带淡入动画
                btnMenuRight?.let { button ->
                    button.setImageResource(R.drawable.ic_menu)
                    button.alpha = 0f
                    button.visibility = View.VISIBLE
                    button.animate()
                        .alpha(1f)
                        .setDuration(200)
                        .setInterpolator(android.view.animation.DecelerateInterpolator())
                        .start()
                }

                Log.d(TAG, "侧边栏隐藏完成")
            }
            .start()
    }

    /**
     * 安排侧边栏自动隐藏
     */
    fun scheduleAutoHide() {
        // 取消之前的任务
        navbarHideHandler.removeCallbacksAndMessages(null)

        // 只有当侧边栏可见时才安排隐藏任务
        val currentState = isVisible.value
        if (currentState == true) {
            // 安排新任务
            navbarHideHandler.postDelayed({
                hideSidebar()
            }, NAVBAR_AUTO_HIDE_DELAY)
        }
    }

    /**
     * 获取侧边栏状态的LiveData
     */
    fun getIsVisible(): MutableLiveData<Boolean> {
        return isVisible
    }

    /**
     * 设置侧边栏状态
     */
    fun setVisible(visible: Boolean) {
        if (visible) {
            showSidebar()
        } else {
            hideSidebar()
        }
    }

    /**
     * 创建暗淡覆盖层
     */
    private fun createDimOverlay() {
        val container = fragmentContainer ?: return
        val parent = container.parent as? ViewGroup ?: return

        // 创建暗淡覆盖层
        dimOverlay = View(container.context).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            setBackgroundColor(0x80000000.toInt()) // 半透明黑色
            alpha = 0f
            visibility = View.GONE
            isClickable = true
            isFocusable = true

            // 点击覆盖层关闭侧边栏
            setOnClickListener {
                hideSidebar()
            }
        }

        // 将覆盖层添加到父容器中，位于Fragment容器之上
        parent.addView(dimOverlay)
    }

    /**
     * 显示暗淡覆盖层（优化版）
     */
    private fun showDimOverlay() {
        dimOverlay?.let { overlay ->
            overlay.visibility = View.VISIBLE
            overlay.animate()
                .alpha(1f)
                .setDuration(250) // 与侧边栏动画同步
                .setInterpolator(android.view.animation.DecelerateInterpolator())
                .withStartAction {
                    // 启用硬件加速
                    overlay.setLayerType(View.LAYER_TYPE_HARDWARE, null)
                }
                .withEndAction {
                    // 恢复默认渲染模式
                    overlay.setLayerType(View.LAYER_TYPE_NONE, null)
                }
                .start()
        }
    }

    /**
     * 隐藏暗淡覆盖层（优化版）
     */
    private fun hideDimOverlay() {
        dimOverlay?.let { overlay ->
            overlay.animate()
                .alpha(0f)
                .setDuration(250) // 与侧边栏动画同步
                .setInterpolator(android.view.animation.AccelerateInterpolator())
                .withStartAction {
                    // 启用硬件加速
                    overlay.setLayerType(View.LAYER_TYPE_HARDWARE, null)
                }
                .withEndAction {
                    overlay.visibility = View.GONE
                    // 恢复默认渲染模式
                    overlay.setLayerType(View.LAYER_TYPE_NONE, null)
                }
                .start()
        }
    }
}
