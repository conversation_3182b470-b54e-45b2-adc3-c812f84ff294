package com.example.aimusicplayer.ui.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.example.aimusicplayer.R
import com.example.aimusicplayer.data.model.Song

/**
 * 搜索结果适配器
 */
class SearchResultsAdapter(
    private val onSongClick: (Song) -> Unit
) : ListAdapter<Song, SearchResultsAdapter.SearchResultViewHolder>(DiffCallback) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SearchResultViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_search_result, parent, false)
        return SearchResultViewHolder(view)
    }

    override fun onBindViewHolder(holder: SearchResultViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class SearchResultViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val albumCover: ImageView = itemView.findViewById(R.id.album_cover)
        private val songTitle: TextView = itemView.findViewById(R.id.song_title)
        private val artistName: TextView = itemView.findViewById(R.id.artist_name)
        private val albumName: TextView = itemView.findViewById(R.id.album_name)

        fun bind(song: Song) {
            songTitle.text = song.name
            artistName.text = song.getArtistNames()
            albumName.text = song.al?.name ?: ""

            // 加载专辑封面
            val coverUrl = song.getAlbumCoverUrl()
            Glide.with(itemView.context)
                .load(coverUrl)
                .placeholder(R.drawable.default_album_art)
                .error(R.drawable.default_album_art)
                .into(albumCover)

            itemView.setOnClickListener {
                onSongClick(song)
            }
        }
    }

    companion object {
        private val DiffCallback = object : DiffUtil.ItemCallback<Song>() {
            override fun areItemsTheSame(oldItem: Song, newItem: Song): Boolean {
                return oldItem.id == newItem.id
            }

            override fun areContentsTheSame(oldItem: Song, newItem: Song): Boolean {
                return oldItem == newItem
            }
        }
    }
}
